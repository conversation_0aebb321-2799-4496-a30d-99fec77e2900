import React from 'react';
import { AnimatedSection } from '@/components/animations';
import { motion } from 'framer-motion';
import { fadeInUp, getReducedMotionVariants } from '@/lib/animations';

const CTAStrip = () => {
  return (
    <AnimatedSection className="w-full bg-gradient-to-r from-pink-500 to-pink-600 py-16 px-6">
      <motion.div
        className="max-w-6xl mx-auto text-center"
        variants={getReducedMotionVariants(fadeInUp)}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-50px" }}
      >
        <motion.h3
          className="text-3xl md:text-5xl font-semibold text-white"
          variants={getReducedMotionVariants(fadeInUp)}
        >
          Let's talk! <a href="/contact" className="underline transition-all">Contact us.</a>
        </motion.h3>
      </motion.div>
    </AnimatedSection>
  );
};

export default CTAStrip;