import React from 'react';
import { motion } from 'framer-motion';
import { cardHover, cardTap, shouldReduceMotion } from '@/lib/animations';

interface AnimatedCardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  href?: string;
  disabled?: boolean;
}

const AnimatedCard: React.FC<AnimatedCardProps> = ({
  children,
  className = '',
  onClick,
  href,
  disabled = false,
}) => {
  const isInteractive = !disabled && (onClick || href);
  const reducedMotion = shouldReduceMotion();

  const hoverAnimation = reducedMotion ? {} : cardHover;
  const tapAnimation = reducedMotion ? {} : cardTap;

  const CardComponent = motion.div;

  return (
    <CardComponent
      className={className}
      whileHover={isInteractive ? hoverAnimation : undefined}
      whileTap={isInteractive ? tapAnimation : undefined}
      onClick={onClick}
      style={{ cursor: isInteractive ? 'pointer' : 'default' }}
    >
      {children}
    </CardComponent>
  );
};

export default AnimatedCard;
