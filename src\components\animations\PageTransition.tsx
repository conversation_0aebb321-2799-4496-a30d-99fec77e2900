import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { pageTransition, getReducedMotionVariants } from '@/lib/animations';

interface PageTransitionProps {
  children: React.ReactNode;
  className?: string;
}

const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  className = '',
}) => {
  const variants = getReducedMotionVariants(pageTransition);

  // Ensure the page starts from the top when the component mounts
  useEffect(() => {
    // Small delay to work with the ScrollToTop component
    const timer = setTimeout(() => {
      if (window.scrollY > 0) {
        window.scrollTo({
          top: 0,
          left: 0,
          behavior: 'smooth'
        });
      }
    }, 50);

    return () => clearTimeout(timer);
  }, []);

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      exit="exit"
      variants={variants}
      className={className}
    >
      {children}
    </motion.div>
  );
};

export default PageTransition;
