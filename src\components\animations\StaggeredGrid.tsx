import React, { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { staggerContainer, staggerItem, getReducedMotionVariants } from '@/lib/animations';

interface StaggeredGridProps {
  children: React.ReactNode;
  className?: string;
  staggerDelay?: number;
  childDelay?: number;
  threshold?: number;
  once?: boolean;
}

const StaggeredGrid: React.FC<StaggeredGridProps> = ({
  children,
  className = '',
  staggerDelay = 0.1,
  childDelay = 0.1,
  threshold = 0.1,
  once = true,
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { 
    threshold, 
    once,
    margin: "-50px 0px -50px 0px"
  });

  const containerVariants = getReducedMotionVariants({
    ...staggerContainer,
    visible: {
      transition: {
        staggerChildren: staggerDelay,
        delayChildren: childDelay,
      },
    },
  });

  const itemVariants = getReducedMotionVariants(staggerItem);

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={containerVariants}
      className={className}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div
          key={index}
          variants={itemVariants}
        >
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
};

export default StaggeredGrid;
