import React from 'react';
import { Link } from 'react-router-dom';
import Header from '@/components/Header';
import CTAStrip from '@/components/CTAStrip';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Users, Globe, TrendingUp, Star, Award } from 'lucide-react';
import { motion } from 'framer-motion';
import { heroContainer, heroTitle, heroSubtitle, staggerContainer, staggerItem, fadeInUp, getReducedMotionVariants } from '@/lib/animations';
import { AnimatedSection, StaggeredGrid, PageTransition } from '@/components/animations';

const Agency = () => {

  const services = [
    {
      title: "Artist management",
      description: "Enhance your DJ career with tailored strategies, industry connections, and global exposure.",
      action: "Start Your Journey",
      icon: <Users className="h-8 w-8 text-primary" />,
    },
    {
      title: "Book an artist",
      description: "Book your next event with the help of our extensive network of DJs and producers.",
      action: "View artists",
      icon: <Globe className="h-8 w-8 text-primary" />,
    },
    {
      title: "Custom",
      description: "Are you looking for something specific? We also offer photo, video, and equipment services.",
      action: "Let's talk",
      icon: <TrendingUp className="h-8 w-8 text-primary" />,
    }
  ];

  const clientLogos = [
    { name: "Absolut", src: "/clients/absolut.png" },
    { name: "Brooklyn", src: "/clients/brooklyn.png" },
    { name: "Brugge", src: "/clients/brugge.png" },
    { name: "Cercle", src: "/clients/cercle.png" },
    { name: "Jet Import", src: "/clients/jetimport.png" },
    { name: "Knokke Heist", src: "/clients/kh2.png" },
    { name: "Pernod Ricard", src: "/clients/pernodricard.png" },
    { name: "Toms Tourney", src: "/clients/tt.png" },
    { name: "VEK", src: "/clients/vek.png" }
  ];

  const caseStudies = [
    {
      title: "Global Festival Circuit",
      description: "Secured headline slots at 15+ major festivals worldwide, transforming an emerging artist into a global headliner with over 2M festival attendees.",
      image: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=300&fit=crop&crop=center",
      metric: "15+ Festivals",
      icon: <Star className="h-6 w-6 text-primary" />
    },
    {
      title: "Digital Transformation",
      description: "300% increase in streaming numbers through strategic digital campaign, reaching 10M+ monthly listeners across all platforms.",
      image: "https://images.unsplash.com/photo-1611532736597-de2d4265fba3?w=400&h=300&fit=crop&crop=center",
      metric: "300% Growth",
      icon: <TrendingUp className="h-6 w-6 text-primary" />
    },
    {
      title: "Brand Partnership",
      description: "Landmark collaboration with luxury fashion house for exclusive collection, generating €2M+ in revenue and global media coverage.",
      image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop&crop=center",
      metric: "€2M+ Revenue",
      icon: <Award className="h-6 w-6 text-primary" />
    }
  ];

  return (
    <PageTransition className="min-h-screen flex flex-col bg-background text-foreground relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 cosmic-grid opacity-30"></div>
      <div className="absolute top-[450px] left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full">
        <div className="w-full h-full opacity-10 bg-primary blur-[120px]"></div>
      </div>

      <div className="relative z-10 flex flex-col min-h-screen">
        <Header />
        <main className="flex-1">
          {/* Hero Section */}
          <AnimatedSection className="relative w-full py-20 md:py-24 mb-10 px-6 md:px-12 flex flex-col items-center justify-center">
            <motion.div
              className="relative z-10 max-w-5xl text-center space-y-8"
              variants={getReducedMotionVariants(heroContainer)}
              initial="hidden"
              animate="visible"
            >
              <motion.div
                className="flex justify-center"
                variants={getReducedMotionVariants(fadeInUp)}
              >
                <span className="border border-muted inline-flex items-center gap-2 px-3 py-1 text-xs font-medium rounded-full text-primary transition-colors cursor-pointer">
                  Leading Artist Management
                </span>
              </motion.div>

              <motion.h1
                className="text-4xl md:text-6xl lg:text-7xl font-semibold text-balance text-foreground"
                variants={getReducedMotionVariants(heroTitle)}
              >
                Shaping the future of <span className="text-primary">artist management</span>
              </motion.h1>

              <motion.p
                className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed"
                variants={getReducedMotionVariants(heroSubtitle)}
              >
                We elevate exceptional artists to global recognition through strategic management, innovative partnerships, and unparalleled industry expertise.
              </motion.p>
            </motion.div>
          </AnimatedSection>

          {/* Stats Section
          <section className="py-16 px-6 md:px-12 border-y border-border bg-muted/30">
            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                <div className="space-y-2">
                  <div className="text-3xl md:text-4xl font-bold text-primary">50+</div>
                  <div className="text-sm text-muted-foreground">Artists Managed</div>
                </div>
                <div className="space-y-2">
                  <div className="text-3xl md:text-4xl font-bold text-primary">200+</div>
                  <div className="text-sm text-muted-foreground">Events Annually</div>
                </div>
                <div className="space-y-2">
                  <div className="text-3xl md:text-4xl font-bold text-primary">10M+</div>
                  <div className="text-sm text-muted-foreground">Streams Generated</div>
                </div>
                <div className="space-y-2">
                  <div className="text-3xl md:text-4xl font-bold text-primary">12</div>
                  <div className="text-sm text-muted-foreground">Years Experience</div>
                </div>
              </div>
            </div>
          </section> */}

        {/* About Section */}
        <AnimatedSection className="py-16 px-6 md:px-12">
          <motion.div
            className="max-w-4xl mx-auto text-center space-y-6"
            variants={getReducedMotionVariants(staggerContainer)}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-50px" }}
          >
            <motion.h2
              className="text-3xl md:text-4xl font-semibold"
              variants={getReducedMotionVariants(staggerItem)}
            >
              Our mission
            </motion.h2>
            <motion.p
              className="text-lg text-muted-foreground leading-relaxed text-balance"
              variants={getReducedMotionVariants(staggerItem)}
            >
              For over a decade, we've been at the forefront of electronic music, discovering and nurturing talent that defines the sound of tomorrow. Our boutique approach ensures every artist receives personalized attention while leveraging our extensive global network to create extraordinary opportunities.
            </motion.p>
            <motion.div
              className="flex justify-center pt-4 gap-4"
              variants={getReducedMotionVariants(staggerItem)}
            >
              <Link to="/contact">
                <Button className="group inline-flex items-center gap-2 bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-3 rounded-full">
                  <span>Work with us</span>
                  <svg
                    className="w-4 h-4 transition-transform duration-200 group-hover:translate-x-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Button>
              </Link>
              <Link to="/agency#services">
                <Button variant="outline" className="group inline-flex items-center gap-2 px-6 py-3 rounded-full">
                  <span>Our Services</span>
                  <svg
                    className="w-4 h-4 transition-transform duration-200 group-hover:translate-y-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                  </svg>
                </Button>
              </Link>
            </motion.div>
          </motion.div>
        </AnimatedSection>

        {/* Services Section */}
        <AnimatedSection id="services" className="py-20 px-6 md:px-12">
          <div className="max-w-6xl mx-auto">
            <StaggeredGrid className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {services.map((service, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.005, y: -2 }}
                  whileTap={{ scale: 0.995 }}
                >
                  <Card className="border-border bg-card hover:border-primary/20 hover:shadow-lg transition-all duration-300 group rounded-xl h-full">
                    <CardContent className="p-8 space-y-6 text-left">
                      <div className="flex items-center justify-between">
                        <div className="p-3 rounded-xl bg-primary/10 group-hover:bg-primary/20 transition-colors duration-300">
                          {service.icon}
                        </div>
                      </div>
                      <h3 className="text-2xl font-semibold">{service.title}</h3>
                      <p className="text-muted-foreground leading-relaxed">{service.description}</p>
                      <Link to="/contact" >
                        <button className="mt-4 font-semibold group/btn inline-flex items-center gap-2 text-lg text-foreground hover:text-primary transition-all duration-200 cursor-pointer">
                          <span>{service.action}</span>
                          <svg
                            className="w-5 h-5 transition-transform duration-200 group-hover/btn:translate-x-1"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </button>
                      </Link>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </StaggeredGrid>
          </div>
        </AnimatedSection>

        {/* Client Logos - Auto Scrolling Strip */}
        <section className="py-12 overflow-hidden border-y border-border client-logos-bg">
          <div className="text-center mb-12">
            <p className="text-white/70">Trusted by the best</p>
          </div>
          <div className="relative">
            {/* Auto-scrolling container */}
            <div className="flex animate-scroll-left ">
              {/* First set of logos */}
              {clientLogos.map((client, index) => (
                <div key={`first-${index}`} className="flex-shrink-0 mx-8">
                  <div className="w-40 h-30 flex items-center justify-center p-4 hover:scale-102 transition-all duration-300">
                    <img
                      src={client.src}
                      alt={client.name}
                      className="max-w-full max-h-full object-contain opacity-90 hover:opacity-100 transition-opacity duration-300 filter grayscale hover:grayscale-0"
                    />
                  </div>
                </div>
              ))}
              {/* Duplicate set for seamless loop */}
              {clientLogos.map((client, index) => (
                <div key={`second-${index}`} className="flex-shrink-0 mx-8">
                  <div className="w-40 h-30 flex items-center justify-center p-4 hover:scale-102 transition-all duration-300">
                    <img
                      src={client.src}
                      alt={client.name}
                      className="max-w-full max-h-full object-contain opacity-60 hover:opacity-100 transition-opacity duration-300 filter grayscale hover:grayscale-0"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Case Studies */}
        <AnimatedSection className="py-20 px-6 md:px-12 bg-background">
          <div className="max-w-6xl mx-auto">
            <motion.div
              className="text-center mb-16"
              variants={getReducedMotionVariants(staggerContainer)}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-50px" }}
            >
              <motion.h2
                className="text-3xl md:text-4xl font-semibold mb-4"
                variants={getReducedMotionVariants(staggerItem)}
              >
                Case studies
              </motion.h2>
              <motion.p
                className="text-muted-foreground text-lg max-w-2xl mx-auto"
                variants={getReducedMotionVariants(staggerItem)}
              >
                Real results from our strategic partnerships and innovative campaigns
              </motion.p>
            </motion.div>

            <StaggeredGrid className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {caseStudies.map((study, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.005, y: -2 }}
                  whileTap={{ scale: 0.995 }}
                >
                  <Card className="border-border bg-card hover:border-primary/20 hover:shadow-lg transition-all duration-300 group overflow-hidden rounded-xl h-full">
                    <div className="aspect-[4/3] overflow-hidden">
                      <img
                        src={study.image}
                        alt={study.title}
                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                    </div>
                    <CardContent className="p-6 space-y-4 text-left">
                      <div className="flex items-center justify-between">
                        <div className="p-2 rounded-xl bg-primary/10">
                          {study.icon}
                        </div>
                        <span className="text-sm font-semibold text-primary">{study.metric}</span>
                      </div>
                      <h3 className="text-xl font-semibold">{study.title}</h3>
                      <p className="text-muted-foreground leading-relaxed text-sm">{study.description}</p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </StaggeredGrid>
          </div>
        </AnimatedSection>
        </main>
        <CTAStrip />
        <Footer />
      </div>
    </PageTransition>
  );
};

export default Agency;