import React, { useState } from 'react';
import Header from '@/components/Header';
import CTAStrip from '@/components/CTAStrip';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Mail, Phone, MapPin } from 'lucide-react';
import { motion } from 'framer-motion';
import { heroContainer, heroTitle, heroSubtitle, staggerContainer, staggerItem, getReducedMotionVariants } from '@/lib/animations';
import { AnimatedSection, StaggeredGrid, PageTransition } from '@/components/animations';

const Contact = () => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    inquiryType: '',
    message: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (value: string) => {
    setFormData(prev => ({ ...prev, inquiryType: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.name || !formData.email || !formData.message || !formData.inquiryType) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    // Simulate form submission
    toast({
      title: "Message Sent!",
      description: "Thank you for your inquiry. We'll get back to you within 24 hours.",
    });

    // Reset form
    setFormData({
      name: '',
      email: '',
      company: '',
      inquiryType: '',
      message: ''
    });
  };

  return (
    <PageTransition className="min-h-screen flex flex-col bg-background text-foreground relative overflow-hidden">
      {/* Cosmic particle effect (background dots) */}
      <div className="absolute inset-0 cosmic-grid opacity-30"></div>

      {/* Gradient glow effect */}
      <div className="absolute top-[450px] left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full">
        <div className="w-full h-full opacity-10 bg-primary blur-[120px]"></div>
      </div>

      <div className="relative z-10 flex flex-col min-h-screen">
        <Header />

        <main className="flex-1 pt-24">
          {/* Hero Section */}
          <AnimatedSection className="relative w-full py-20 md:py-28 px-6 md:px-12 flex flex-col items-center justify-center">
            <motion.div
              className="relative z-10 max-w-5xl text-center space-y-8"
              variants={getReducedMotionVariants(heroContainer)}
              initial="hidden"
              animate="visible"
            >
              <motion.div
                className="flex justify-center"
                variants={getReducedMotionVariants(staggerItem)}
              >
                <span className="inline-flex items-center gap-2 px-3 py-1 text-xs font-medium rounded-full bg-muted text-primary">
                  <span className="flex h-2 w-2 rounded-full bg-primary"></span>
                  Get in Touch
                </span>
              </motion.div>

              <motion.h1
                className="text-4xl md:text-6xl lg:text-7xl font-semibold tracking-tighter text-balance text-foreground"
                variants={getReducedMotionVariants(heroTitle)}
              >
                Let's <span className="text-primary">Connect</span>
              </motion.h1>

              <motion.p
                className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed"
                variants={getReducedMotionVariants(heroSubtitle)}
              >
                Ready to take your career to the next level? Let's discuss how we can work together.
              </motion.p>
            </motion.div>
          </AnimatedSection>

        {/* Contact Information */}
        <AnimatedSection className="py-16 px-6 md:px-12">
          <div className="max-w-4xl mx-auto">
            <StaggeredGrid className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="flex items-start gap-4">
                <div className="rounded-lg bg-primary/10 p-3 flex-shrink-0">
                  <Mail className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold mb-2">Email</h3>
                  <p className="text-muted-foreground"><EMAIL></p>
                  <p className="text-muted-foreground"><EMAIL></p>
                </div>
              </div>
              <div className="flex items-start gap-4">
                <div className="rounded-lg bg-primary/10 p-3 flex-shrink-0">
                  <Phone className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold mb-2">Phone</h3>
                  <p className="text-muted-foreground">+****************</p>
                  <p className="text-muted-foreground">+44 20 7123 4567</p>
                </div>
              </div>
              <div className="flex items-start gap-4">
                <div className="rounded-lg bg-primary/10 p-3 flex-shrink-0">
                  <MapPin className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold mb-2">Offices</h3>
                  <p className="text-muted-foreground">Los Angeles, CA</p>
                  <p className="text-muted-foreground">London, UK</p>
                  <p className="text-muted-foreground">Berlin, Germany</p>
                </div>
              </div>
            </StaggeredGrid>
          </div>
        </AnimatedSection>

        {/* Contact Form */}
        <AnimatedSection className="py-16 px-6 md:px-12">
          <div className="max-w-2xl mx-auto">
            <motion.div
              className="text-center mb-8"
              variants={getReducedMotionVariants(staggerContainer)}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, margin: "-50px" }}
            >
              <motion.h2
                className="text-3xl md:text-4xl font-semibold mb-4"
                variants={getReducedMotionVariants(staggerItem)}
              >
                Send us a Message
              </motion.h2>
              <motion.p
                className="text-muted-foreground"
                variants={getReducedMotionVariants(staggerItem)}
              >
                Fill out the form below and we'll get back to you as soon as possible.
              </motion.p>
            </motion.div>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name *</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Your full name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="company">Company/Organization</Label>
                <Input
                  id="company"
                  name="company"
                  value={formData.company}
                  onChange={handleInputChange}
                  placeholder="Your company or organization"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="inquiryType">Type of Inquiry *</Label>
                <Select onValueChange={handleSelectChange} value={formData.inquiryType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select inquiry type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="artist-submission">Artist Submission</SelectItem>
                    <SelectItem value="booking-request">Booking Request</SelectItem>
                    <SelectItem value="partnership">Partnership Opportunity</SelectItem>
                    <SelectItem value="press-media">Press & Media</SelectItem>
                    <SelectItem value="general">General Inquiry</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="message">Message *</Label>
                <Textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  placeholder="Tell us about your inquiry..."
                  rows={6}
                  required
                />
              </div>
              
              <Button type="submit" className="w-full">
                Send Message
              </Button>
            </form>
          </div>
        </AnimatedSection>
      </main>
      <CTAStrip />
      <Footer />
      </div>
    </PageTransition>
  );
};

export default Contact;